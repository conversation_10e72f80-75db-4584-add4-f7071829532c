#include <iostream>
#include <string>
 using namespace std;

 class book{
    public:
       int qu;
       int price;
    void show();
    book(int n){
        qu=n;
        price=10*qu;
    }
};
 void book::show(){
    cout<<qu*price<<endl;
}
 int main(int argc,const char*argv[]){
    book book1[5]=
        {book(1),book(2),book(3),book(4),book(5)};
    for(int i=0;i<5;i++){
        book1[i].show();
    }

return 0;
}
