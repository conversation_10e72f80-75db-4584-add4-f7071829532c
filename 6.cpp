struct hostent{
    char*h_name;
    char**h_ailiases;
    int h_addrtype;
    int h_length;
    char**h_addr_list;
};
#include<netdb.h>
#include<sys/socket.h>
#include<stdio>
#include<arpa/inet.h>
int main(int argc,char**argv){
    chat*ptr,**pptr;
    struct hostent*hptr=NULL;
    char str[32]={0};   //用于存放ip地址的字节序
    if(argc<2){
        printf("please input an address,eg:./a.out www.baidu.com\n");
        return 0;
    }
}
//获取主机域名
{ptr=argv[1];
//调用gethostbyname();
if((hptr=gethostbyname(ptr))==NULL){  //调用失败
    printf("gethostbyname error for host:%s\n,ptr");
}
return 0;
}
else{  //调用成功
    printf("officil hostname:%s\n,hptr->h_name");
}
//若主机有多个别名
for(pptr=hptr->h_ailiases;*pptr!=NULL;pptr++){
    printf("ailiases:%s\n,*pptr");
}
return 0;
//根据地址类型将地址打印出来
switch(hptr->h_addrtype){
    case AF_INET:   //代表ipv4
    case AF_INET6:  //代表ipv6
       pptr=hptr->h_addr_list;
    for(;*pptr!=NULL;pptr++){
        printf("address:%s\n",inet_ntop(hptr->h_addrtype,*pptr,str,sizeof(str)));
    }
}
return 0;