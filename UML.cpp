#include <iostream>
#include <string>
#include <vector>
#include <unordered_map>

// Account class
class Account {
private:
    std::string accountNumber;
    std::string pin;
    double balance;

public:
    Account(std::string accNum, std::string p, double bal)
        : accountNumber(accNum), pin(p), balance(bal) {}

    bool authenticate(const std::string& p) {
        return pin == p;
    }

    double getBalance() const {
        return balance;
    }

    void deposit(double amount) {
        balance += amount;
    }

    void withdraw(double amount) {
        if (balance >= amount) {
            balance -= amount;
        } else {
            std::cerr << "Insufficient funds" << std::endl;
        }
    }
};

// Customer class
class Customer {
private:
    std::string name;
    std::vector<Account> accounts;

public:
    Customer(std::string n) : name(n) {}

    void addAccount(const Account& account) {
        accounts.push_back(account);
    }

    Account* getAccount(const std::string& accountNumber) {
        for (auto& acc : accounts) {
            if (acc.getAccountNumber() == accountNumber) {
                return &acc;
            }
        }
        return nullptr;
    }
};

// ATM class
class ATM {
private:
    std::string machineID;
    std::string location;
    double cashBalance;

public:
    ATM(std::string id, std::string loc, double bal)
        : machineID(id), location(loc), cashBalance(bal) {}

    double checkBalance(Account& account) {
        return account.getBalance();
    }

    void depositFunds(Account& account, double amount) {
        account.deposit(amount);
        cashBalance += amount;
    }

    void withdrawCash(Account& account, double amount) {
        if (cashBalance >= amount) {
            account.withdraw(amount);
            cashBalance -= amount;
        } else {
            std::cerr << "ATM cash balance insufficient" << std::endl;
        }
    }

    void transferFunds(Account& source, Account& destination, double amount) {
        if (source.getBalance() >= amount) {
            source.withdraw(amount);
            destination.deposit(amount);
        } else {
            std::cerr << "Source account balance insufficient" << std::endl;
        }
    }
};

// ATMTechnician class
class ATMTechnician {
private:
    std::string technicianID;
    std::string name;

public:
    ATMTechnician(std::string id, std::string n) : technicianID(id), name(n) {}

    void maintainATM(ATM& atm) {
        // Maintenance logic
        std::cout << "Maintaining ATM " << atm.getMachineID() << std::endl;
    }

    void repairATM(ATM& atm) {
        // Repair logic
        std::cout << "Repairing ATM " << atm.getMachineID() << std::endl;
    }

    void replenishCash(ATM& atm, double amount) {
        atm.setCashBalance(atm.getCashBalance() + amount);
        std::cout << "Replenished ATM " << atm.getMachineID() << " with " << amount << " cash" << std::endl;
    }
};

// Bank class
class Bank {
private:
    std::string name;
    std::unordered_map<std::string, ATM> atms;
    std::unordered_map<std::string, Customer> customers;

public:
    Bank(std::string n) : name(n) {}

    void addATM(const ATM& atm) {
        atms[atm.getMachineID()] = atm;
    }

    ATM* getATM(const std::string& machineID) {
        if (atms.find(machineID) != atms.end()) {
            return &atms[machineID];
        }
        return nullptr;
    }

    void addCustomer(const Customer& customer) {
        customers[customer.getName()] = customer;
    }

    Customer* getCustomer(const std::string& name) {
        if (customers.find(name) != customers.end()) {
            return &customers[name];
        }
        return nullptr;
    }
};

int main() {
    // Create a bank
    Bank myBank("My Bank");

    // Create customers and accounts
    Customer john("John Doe");
    Account johnAccount("123456", "1234", 1000.0);
    john.addAccount(johnAccount);

    Customer jane("Jane Smith");
    Account janeAccount("654321", "4321", 2000.0);
    jane.addAccount(janeAccount);

    // Add customers to the bank
    myBank.addCustomer(john);
    myBank.addCustomer(jane);

    // Create ATMs
    ATM atm1("ATM001", "Location A", 5000.0);
    ATM atm2("ATM002", "Location B", 10000.0);

    // Add ATMs to the bank
    myBank.addATM(atm1);
    myBank.addATM(atm2);

    // Simulate an ATM transaction
    ATM* atm = myBank.getATM("ATM001");
    Account* account = myBank.getCustomer("John Doe")->getAccount("123456");

    if (atm && account) {
        std::cout << "John's current balance: " << atm->checkBalance(*account) << std::endl;
        atm->depositFunds(*account, 500.0);
        std::cout << "John's balance after deposit: " << atm->checkBalance(*account) << std::endl;
    }

    return 0;
}
