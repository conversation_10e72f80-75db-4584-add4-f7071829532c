#include <iostream>
#include <vector>
#include <deque>
#include <algorithm>
#include <ctime>
#include <conio.h> // For _getch()

class SimonGame {
private:
    std::vector<int> pattern;
    std::vector<int> userInput;
    int score;
    std::vector<int> topScores;

    void generateNextPattern() {
        pattern.push_back(rand() % 4); // Generate a random number between 0 and 3
    }

    void displayPattern() {
        std::cout << "Pattern: ";
        for (int btn : pattern) {
            std::cout << btn << " ";
        }
        std::cout << std::endl;
    }

    bool checkUserInput() {
        return pattern == userInput;
    }

    void clearUserInput() {
        userInput.clear();
    }

    void addToTopScores(int score) {
        topScores.push_back(score);
        std::sort(topScores.rbegin(), topScores.rend());
        if (topScores.size() > 10) {
            topScores.pop_back();
        }
    }

    void displayTopScores() {
        std::cout << "Top 10 Scores:\n";
        for (int score : topScores) {
            std::cout << score << std::endl;
        }
    }

public:
    SimonGame() : score(0) {
        srand(time(0));
    }

    void startGame() {
        while (true) {
            generateNextPattern();
            displayPattern();

            clearUserInput();
            std::cout << "Repeat the pattern: ";

            for (int i = 0; i < pattern.size(); ++i) {
                int input;
                std::cin >> input;
                userInput.push_back(input);
            }

            if (checkUserInput()) {
                std::cout << "Correct!\n";
                score++;
            } else {
                std::cout << "Wrong! Game Over.\n";
                addToTopScores(score);
                displayTopScores();
                break;
            }
        }
    }
};
