#include <iostream>
#include <vector>
#include <cstdlib>
#include <ctime>
#include <conio.h> // For _getch() function (Windows only)

// Color constants
const char RED = 'R';
const char GREEN = 'G';
const char BLUE = 'B';
const char YELLOW = 'Y';

// Function to generate a random color
char getRandomColor() {
    char colors[] = {RED, GREEN, BLUE, YELLOW};
    int index = rand() % 4;
    return colors[index];
}

// Function to display the color sequence
void displaySequence(const std::vector<char>& sequence) {
    for (char color : sequence) {
        std::cout << color << " ";
        _sleep(1000); // Sleep for 1 second (1000 milliseconds)
    }
    std::cout << std::endl;
}

// Function to get user input for the color sequence
std::vector<char> getUserInput(int length) {
    std::vector<char> userInput;
    char input;
    
    std::cout << "Enter your sequence: ";
    for (int i = 0; i < length; ++i) {
        input = _getch(); // Use _getch() to get character input without pressing enter
        userInput.push_back(input);
        std::cout << input << " ";
    }
    std::cout << std::endl;
    
    return userInput;
}

// Main game function
int main() {
    srand(static_cast<unsigned int>(time(0))); // Seed random number generator
    
    int level = 1;
    std::vector<char> sequence;
    
    std::cout << "Welcome to the Simon Game!" << std::endl;
    
    while (true) {
        // Add a new random color to the sequence
        sequence.push_back(getRandomColor());
        
        std::cout << "Level " << level << std::endl;
        displaySequence(sequence);
        
        // Get user input
        std::vector<char> userInput = getUserInput(sequence.size());
        
        // Check if user input matches the sequence
        if (userInput == sequence) {
            std::cout << "Correct! You've moved to the next level." << std::endl;
            ++level;
        } else {
            std::cout << "Incorrect! Game Over. Your score: " << level - 1 << std::endl;
            break;
        }
    }
    
    return 0;
}
