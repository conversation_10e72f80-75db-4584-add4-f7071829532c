//shape类
#include<iostream>
using namespace std;

class Cshape {
	protected:
		int width;
		int height;
	public:
	    void setwidth(int w){
		    width=w;
	}
     	void setheight(int h){
		    height=h;
	}
};
//派生类
class Cretangle:public Cshape {
	public:
		int getArea() {
			return width*height;
		}
};
int main(int argc,const char*argv[]){
	Cretangle rect;
	rect.setwidth(5);
	rect.setheight(7);
	cout<<"面积:"<<rect.getArea()<<endl;           
	return 0;
}
